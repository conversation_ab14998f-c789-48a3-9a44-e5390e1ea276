=== ADAPTIVE CIRCUIT ANALYSIS ===
Source file: input/ACF2.cct

Circuit Topology Analysis:
==================================================

Component Count:
  Inductors: 8
  Capacitors: 3
  Resistors: 6

Inductor Classification:
  Neutral inductors found: Ln1, Ln2
  Phase inductors found: Lu1, Lv1, Lw1, Lu2, Lv2, Lw2

Circuit Structure Analysis:
  Structure: Dual neutral inductors - Primary/Secondary system
  Primary phases: 3 (Lu1, Lv1, Lw1)
  Secondary phases: 3 (Lu2, Lv2, Lw2)

Extended <PERSON><PERSON><PERSON>'s Voltage Law (KVL) Analysis:
For each independent loop in the circuit:

Loop 1: Ru2 → Rv2 → Rw2
  KVL: V_ru2 + V_rv2 + V_rw2 = 0

Loop Current Analysis:
Independent current relationships:

  Node 1: -I_lu1 +I_ru1 = 0
  Node 2: +I_lu1 -I_lu2 -I_cu2 = 0
  Node 3: -I_lv1 +I_rv1 = 0
  Node 4: +I_lv1 -I_lv2 -I_cv2 = 0
  Node 5: -I_lw1 +I_rw1 = 0
  Node 6: +I_lw1 -I_lw2 -I_cw2 = 0
  Node 12: +I_lu2 -I_ru2 = 0
  Node 13: +I_lv2 -I_rv2 = 0
  Node 14: +I_lw2 -I_rw2 = 0

Energy Analysis:
Total system energy:

Magnetic energy (inductors):
  E_lu1 = (1/2) * Lu1 * I_lu1²
  E_lv1 = (1/2) * Lv1 * I_lv1²
  E_lw1 = (1/2) * Lw1 * I_lw1²
  E_ln1 = (1/2) * Ln1 * I_ln1²
  E_lu2 = (1/2) * Lu1 * I_lu2²
  E_lv2 = (1/2) * Lv1 * I_lv2²
  E_lw2 = (1/2) * Lw1 * I_lw2²
  E_ln2 = (1/2) * Ln1 * I_ln2²
  Total magnetic energy: E_L = E_lu1 + E_lv1 + E_lw1 + E_ln1 + E_lu2 + E_lv2 + E_lw2 + E_ln2

Electric energy (capacitors):
  E_cu2 = (1/2) * Cu1 * V_cu2²
  E_cv2 = (1/2) * Cv1 * V_cv2²
  E_cw2 = (1/2) * Cw1 * V_cw2²
  Total electric energy: E_C = E_cu2 + E_cv2 + E_cw2

Total system energy: E_total = E_L + E_C

Adaptive System Matrix Analysis:
==================================================

Inductor Matrix Analysis:

Dual Neutral System:

Primary neutral: Ln1
Secondary neutral: Ln2
Primary phases: Lu1, Lv1, Lw1
Secondary phases: Lu2, Lv2, Lw2

Primary system matrix:
⎡d(Ilu1)/dt⎤
⎢d(Ilv1)/dt⎥
⎣d(Ilw1)/dt⎦

Matrix structure:
Row 1 (Lu1): (Lu1+Ln1) Ln1 Ln1 
Row 2 (Lv1): Ln1 (Lv1+Ln1) Ln1 
Row 3 (Lw1): Ln1 Ln1 (Lw1+Ln1) 

Secondary system matrix:
⎡d(Ilu2)/dt⎤
⎢d(Ilv2)/dt⎥
⎣d(Ilw2)/dt⎦

Matrix structure:
Row 1 (Lu2): (Lu1+Ln1) Ln1 Ln1 
Row 2 (Lv2): Ln1 (Lv1+Ln1) Ln1 
Row 3 (Lw2): Ln1 Ln1 (Lw1+Ln1) 

Capacitor Matrix Analysis:

Multiple capacitors detected:
  Cu2: d(Vcu2)/dt = Icu2 / Cu1
  Cv2: d(Vcv2)/dt = Icv2 / Cv1
  Cw2: d(Vcw2)/dt = Icw2 / Cw1

Capacitor voltage state vector:
V_cap = [Vcu2, Vcv2, Vcw2]ᵀ

Combined System Analysis:

Total system states: 11
  Inductor currents: 8
  Capacitor voltages: 3

State-space representation:
dx/dt = A*x + B*u

Where x contains:
  Ilu1 (inductor current)
  Ilv1 (inductor current)
  Ilw1 (inductor current)
  Iln1 (inductor current)
  Ilu2 (inductor current)
  Ilv2 (inductor current)
  Ilw2 (inductor current)
  Iln2 (inductor current)
  Vcu2 (capacitor voltage)
  Vcv2 (capacitor voltage)
  Vcw2 (capacitor voltage)

Unified Circuit Analysis:
==================================================

Circuit Component Analysis:
  Total inductors: 8
  Neutral inductors: ['Ln1', 'Ln2']
  Phase inductors: ['Lu1', 'Lv1', 'Lw1', 'Lu2', 'Lv2', 'Lw2']
  Primary phases: ['Lu1', 'Lv1', 'Lw1']
  Secondary phases: ['Lu2', 'Lv2', 'Lw2']
  Capacitors: 3
  Resistors: 6

Multi-Inductor System Analysis:

Dual Neutral System Analysis:

System Structure:
  Primary phases: Lu1, Lv1, Lw1
  Secondary phases: Lu2, Lv2, Lw2
  Primary neutral: Ln1
  Secondary neutral: Ln2

Primary System (with neutral coupling):
Complete Matrix Equation:

⎡d(Ilu1)/dt⎤   ⎡Lu1+Ln1 Ln1 Ln1⎤⁻¹ ⎡Vlu1_eff⎤
⎢d(Ilv1)/dt⎥ = ⎢Ln1 Lv1+Ln1 Ln1⎥   ⎢Vlv1_eff⎥
⎣d(Ilw1)/dt⎦   ⎣Ln1 Ln1 Lw1+Ln1⎦   ⎣Vlw1_eff⎦

Matrix Components:

Current derivative vector [dI/dt]:
⎡d(Ilu1)/dt⎤
⎢d(Ilv1)/dt⎥
⎣d(Ilw1)/dt⎦

Inductance matrix [L]:
⎡Lu1+Ln1 Ln1 Ln1⎤
⎢Ln1 Lv1+Ln1 Ln1⎥
⎣Ln1 Ln1 Lw1+Ln1⎦

Voltage vector [V]:
⎡Vlu1_eff⎤
⎢Vlv1_eff⎥
⎣Vlw1_eff⎦


Secondary System (with neutral coupling):
Complete Matrix Equation:

⎡d(Ilu2)/dt⎤   ⎡Lu1+Ln1 Ln1 Ln1⎤⁻¹ ⎡Vlu2_eff⎤
⎢d(Ilv2)/dt⎥ = ⎢Ln1 Lv1+Ln1 Ln1⎥   ⎢Vlv2_eff⎥
⎣d(Ilw2)/dt⎦   ⎣Ln1 Ln1 Lw1+Ln1⎦   ⎣Vlw2_eff⎦

Matrix Components:

Current derivative vector [dI/dt]:
⎡d(Ilu2)/dt⎤
⎢d(Ilv2)/dt⎥
⎣d(Ilw2)/dt⎦

Inductance matrix [L]:
⎡Lu1+Ln1 Ln1 Ln1⎤
⎢Ln1 Lv1+Ln1 Ln1⎥
⎣Ln1 Ln1 Lw1+Ln1⎦

Voltage vector [V]:
⎡Vlu2_eff⎤
⎢Vlv2_eff⎥
⎣Vlw2_eff⎦


Capacitor Analysis:
  Multiple capacitors (3):
    Cu2: d(Vcu2)/dt = Icu2 / Cu1
    Cv2: d(Vcv2)/dt = Icv2 / Cv1
    Cw2: d(Vcw2)/dt = Icw2 / Cw1

Resistor Analysis:
  Multiple resistors (6):
    Ru2: Iru2 = Vru2 / Ru1
    Rv2: Irv2 = Vrv2 / Rv1
    Rw2: Irw2 = Vrw2 / Rw1
    Rw1: Irw1 = Vrw1 / Rw1
    Rv1: Irv1 = Vrv1 / Rv1
    Ru1: Iru1 = Vru1 / Ru1

System Coupling Analysis:
Reactive component coupling:
  Total reactive components: 11
  Inductors: 8 (magnetic energy storage)
  Capacitors: 3 (electric energy storage)

State-space representation:
dx/dt = A*x + B*u
Where x contains:
  Ilu1 (inductor current)
  Ilv1 (inductor current)
  Ilw1 (inductor current)
  Iln1 (inductor current)
  Ilu2 (inductor current)
  Ilv2 (inductor current)
  Ilw2 (inductor current)
  Iln2 (inductor current)
  Vcu2 (capacitor voltage)
  Vcv2 (capacitor voltage)
  Vcw2 (capacitor voltage)

Complete System Matrix Equations:
==================================================

Primary System Matrix Equation:
Complete Matrix Equation:

⎡d(Ilu1)/dt⎤   ⎡Lu1+Ln1 Ln1 Ln1⎤⁻¹ ⎡Vlu1_eff⎤
⎢d(Ilv1)/dt⎥ = ⎢Ln1 Lv1+Ln1 Ln1⎥   ⎢Vlv1_eff⎥
⎣d(Ilw1)/dt⎦   ⎣Ln1 Ln1 Lw1+Ln1⎦   ⎣Vlw1_eff⎦

Matrix Components:

Current derivative vector [dI/dt]:
⎡d(Ilu1)/dt⎤
⎢d(Ilv1)/dt⎥
⎣d(Ilw1)/dt⎦

Inductance matrix [L]:
⎡Lu1+Ln1 Ln1 Ln1⎤
⎢Ln1 Lv1+Ln1 Ln1⎥
⎣Ln1 Ln1 Lw1+Ln1⎦

Voltage vector [V]:
⎡Vlu1_eff⎤
⎢Vlv1_eff⎥
⎣Vlw1_eff⎦


Secondary System Matrix Equation:
Complete Matrix Equation:

⎡d(Ilu2)/dt⎤   ⎡Lu1+Ln1 Ln1 Ln1⎤⁻¹ ⎡Vlu2_eff⎤
⎢d(Ilv2)/dt⎥ = ⎢Ln1 Lv1+Ln1 Ln1⎥   ⎢Vlv2_eff⎥
⎣d(Ilw2)/dt⎦   ⎣Ln1 Ln1 Lw1+Ln1⎦   ⎣Vlw2_eff⎦

Matrix Components:

Current derivative vector [dI/dt]:
⎡d(Ilu2)/dt⎤
⎢d(Ilv2)/dt⎥
⎣d(Ilw2)/dt⎦

Inductance matrix [L]:
⎡Lu1+Ln1 Ln1 Ln1⎤
⎢Ln1 Lv1+Ln1 Ln1⎥
⎣Ln1 Ln1 Lw1+Ln1⎦

Voltage vector [V]:
⎡Vlu2_eff⎤
⎢Vlv2_eff⎥
⎣Vlw2_eff⎦


Combined System Matrix (Primary + Secondary):

Total system states: 11
  Inductor currents: 8
  Capacitor voltages: 3

State vector [x]:
⎡Ilu1⎤
⎢Ilv1⎥
⎢Ilw1⎥
⎢Ilu2⎥
⎢Ilv2⎥
⎢Ilw2⎥
⎢Iln1⎥
⎢Iln2⎥
⎢Vcu2⎥
⎢Vcv2⎥
⎣Vcw2⎦

System equation: dx/dt = A*x + B*u
Where A is a 11×11 system matrix
