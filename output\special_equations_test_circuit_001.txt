=== ADAPTIVE CIRCUIT ANALYSIS ===
Source file: input/test_circuit_001.cct

Circuit Topology Analysis:
==================================================

Component Count:
  Inductors: 3
  Capacitors: 3
  Resistors: 9

Inductor Classification:
  No neutral inductors detected
  Phase inductors found: Lu1, Lv1, Lw1

Circuit Structure Analysis:
  Structure: No neutral inductors - Independent phase system
  Single system phases: 3 (Lu1, Lv1, Lw1)

Extended <PERSON><PERSON><PERSON>'s Voltage Law (KVL) Analysis:
For each independent loop in the circuit:

Loop 1: Ru1 → Rv1 → Rw1
  KVL: V_ru1 + V_rv1 + V_rw1 = 0

Loop Current Analysis:
Independent current relationships:

  Node 2: +I_ru1 -I_lu1 = 0
  Node 3: +I_lu1 -I_ru2 -I_ru3 = 0
  Node 5: +I_rw1 -I_lw1 = 0
  Node 6: +I_lw1 -I_rw2 -I_rw3 = 0
  Node 8: +I_rv1 -I_lv1 = 0
  Node 9: +I_lv1 -I_rv2 -I_rv3 = 0
  Node 11: -I_cu1 +I_ru2 = 0
  Node 12: -I_cv1 +I_rv2 = 0
  Node 13: -I_cw1 +I_rw2 = 0

Energy Analysis:
Total system energy:

Magnetic energy (inductors):
  E_lu1 = (1/2) * Lu1 * I_lu1²
  E_lv1 = (1/2) * Lv1 * I_lv1²
  E_lw1 = (1/2) * Lw1 * I_lw1²
  Total magnetic energy: E_L = E_lu1 + E_lv1 + E_lw1

Electric energy (capacitors):
  E_cu1 = (1/2) * Cu1 * V_cu1²
  E_cv1 = (1/2) * Cv1 * V_cv1²
  E_cw1 = (1/2) * Cw1 * V_cw1²
  Total electric energy: E_C = E_cu1 + E_cv1 + E_cw1

Total system energy: E_total = E_L + E_C

Adaptive System Matrix Analysis:
==================================================

Inductor Matrix Analysis:

Independent Inductor System (No Neutral Coupling):
Each inductor operates independently:
  d(Ilu1)/dt = Vlu1 / Lu1
  d(Ilv1)/dt = Vlv1 / Lv1
  d(Ilw1)/dt = Vlw1 / Lw1

Capacitor Matrix Analysis:

Multiple capacitors detected:
  Cu1: d(Vcu1)/dt = Icu1 / Cu1
  Cv1: d(Vcv1)/dt = Icv1 / Cv1
  Cw1: d(Vcw1)/dt = Icw1 / Cw1

Capacitor voltage state vector:
V_cap = [Vcu1, Vcv1, Vcw1]ᵀ

Combined System Analysis:

Total system states: 6
  Inductor currents: 3
  Capacitor voltages: 3

State-space representation:
dx/dt = A*x + B*u

Where x contains:
  Ilu1 (inductor current)
  Ilv1 (inductor current)
  Ilw1 (inductor current)
  Vcu1 (capacitor voltage)
  Vcv1 (capacitor voltage)
  Vcw1 (capacitor voltage)

Unified Circuit Analysis:
==================================================

Circuit Component Analysis:
  Total inductors: 3
  Neutral inductors: None
  Phase inductors: ['Lu1', 'Lv1', 'Lw1']
  Primary phases: ['Lu1', 'Lv1', 'Lw1']
  Secondary phases: None
  Capacitors: 3
  Resistors: 9

Multi-Inductor System Analysis:

Independent Inductor System (No Neutral Coupling):
Each inductor operates independently:
  d(Ilu1)/dt = Vlu1 / Lu1
  d(Ilv1)/dt = Vlv1 / Lv1
  d(Ilw1)/dt = Vlw1 / Lw1

Capacitor Analysis:
  Multiple capacitors (3):
    Cu1: d(Vcu1)/dt = Icu1 / Cu1
    Cv1: d(Vcv1)/dt = Icv1 / Cv1
    Cw1: d(Vcw1)/dt = Icw1 / Cw1

Resistor Analysis:
  Multiple resistors (9):
    Ru1: Iru1 = Vru1 / Ru1
    Rv1: Irv1 = Vrv1 / Rv1
    Rw1: Irw1 = Vrw1 / Rw1
    Ru2: Iru2 = Vru2 / Ru2
    Ru3: Iru3 = Vru3 / Ru3
    Rv2: Irv2 = Vrv2 / Rv2
    Rw2: Irw2 = Vrw2 / Rw2
    Rv3: Irv3 = Vrv3 / Rv3
    Rw3: Irw3 = Vrw3 / Rw3

System Coupling Analysis:
Reactive component coupling:
  Total reactive components: 6
  Inductors: 3 (magnetic energy storage)
  Capacitors: 3 (electric energy storage)

State-space representation:
dx/dt = A*x + B*u
Where x contains:
  Ilu1 (inductor current)
  Ilv1 (inductor current)
  Ilw1 (inductor current)
  Vcu1 (capacitor voltage)
  Vcv1 (capacitor voltage)
  Vcw1 (capacitor voltage)
