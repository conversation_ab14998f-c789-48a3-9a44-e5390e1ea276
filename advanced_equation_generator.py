"""
Advanced Equation Generator for Complex Circuit Analysis
Generates comprehensive equations for R, L, C circuits using KVL and KCL
"""

import numpy as np
from typing import Dict, <PERSON>, Tuple, Optional
from dataclasses import dataclass
from advanced_circuit_parser import AdvancedCircuitParser, CircuitComponent, ComponentType

@dataclass
class ComponentEquation:
    """Represents equations for a circuit component"""
    name: str
    component_type: str
    value: str
    node1: int
    node2: int
    voltage_equation: str
    current_equation: str
    current_equation_discrete: str
    voltage_integration: Optional[str] = None
    additional_equations: List[str] = None

class AdvancedEquationGenerator:
    """Generates mathematical equations for complex circuit analysis"""

    def __init__(self, parser: AdvancedCircuitParser, input_filename: str = ""):
        self.parser = parser
        self.input_filename = input_filename
        self.component_equations: Dict[str, ComponentEquation] = {}
        self.kvl_equations: List[str] = []
        self.kcl_equations: List[str] = []
        self.matrix_equations: List[str] = []
    
    def generate_all_equations(self) -> Dict[str, ComponentEquation]:
        """Generate equations for all components in the circuit"""
        # Generate individual component equations
        self._generate_resistor_equations()
        self._generate_inductor_equations()
        self._generate_capacitor_equations()
        
        # Generate system equations
        self._generate_kvl_equations()
        self._generate_kcl_equations()
        self._generate_matrix_equations()
        
        return self.component_equations
    
    def _generate_resistor_equations(self) -> None:
        """Generate equations for resistors"""
        resistors = self.parser.get_resistors()
        
        for res_name, res_component in resistors.items():
            equation = self._generate_resistor_equation(res_component)
            self.component_equations[res_name] = equation
    
    def _generate_resistor_equation(self, resistor: CircuitComponent) -> ComponentEquation:
        """Generate equations for a single resistor"""
        res_name = resistor.name
        resistance = resistor.value
        node1 = resistor.node1
        node2 = resistor.node2
        
        # Generate voltage names
        v_node1 = self._get_voltage_name(node1)
        v_node2 = self._get_voltage_name(node2)
        v_res = f"V_{res_name.lower()}"
        i_res = f"I_{res_name.lower()}"
        
        # Voltage equation: V_res = V_node1 - V_node2
        voltage_equation = f"{v_res} = {v_node1} - {v_node2}"
        
        # Current equation: I_res = V_res / R (Ohm's law)
        current_equation = f"{i_res} = {v_res} / {resistance}"
        
        # Discrete current equation (same for resistor)
        current_equation_discrete = f"{i_res} = ({v_node1} - {v_node2}) / {resistance}"
        
        return ComponentEquation(
            name=res_name,
            component_type="Resistor",
            value=resistance,
            node1=node1,
            node2=node2,
            voltage_equation=voltage_equation,
            current_equation=current_equation,
            current_equation_discrete=current_equation_discrete
        )
    
    def _generate_inductor_equations(self) -> None:
        """Generate equations for inductors"""
        inductors = self.parser.get_inductors()
        
        for ind_name, ind_component in inductors.items():
            equation = self._generate_inductor_equation(ind_component)
            self.component_equations[ind_name] = equation
    
    def _generate_inductor_equation(self, inductor: CircuitComponent) -> ComponentEquation:
        """Generate equations for a single inductor"""
        ind_name = inductor.name
        inductance = inductor.value
        node1 = inductor.node1
        node2 = inductor.node2
        
        # Generate voltage names
        v_node1 = self._get_voltage_name(node1)
        v_node2 = self._get_voltage_name(node2)
        v_ind = f"V_{ind_name.lower()}"
        i_ind = f"I_{ind_name.lower()}"
        
        # Voltage equation: V_ind = V_node1 - V_node2
        voltage_equation = f"{v_ind} = {v_node1} - {v_node2}"
        
        # Current equation: V_ind = L * d(I_ind)/dt
        current_equation = f"{v_ind} = {inductance} * d({i_ind})/dt"
        
        # Discrete current equation: I_ind = I_ind_old + (V_ind * dt) / L
        current_equation_discrete = (
            f"{i_ind} = {i_ind}_old + "
            f"(({v_node1} - {v_node2}) * dt) / {inductance}"
        )
        
        # Current integration equation: I_ind = (1/L) * ∫V_ind dt
        voltage_integration = f"{i_ind} = (1/{inductance}) * ∫{v_ind} dt"
        
        return ComponentEquation(
            name=ind_name,
            component_type="Inductor",
            value=inductance,
            node1=node1,
            node2=node2,
            voltage_equation=voltage_equation,
            current_equation=current_equation,
            current_equation_discrete=current_equation_discrete,
            voltage_integration=voltage_integration
        )
    
    def _generate_capacitor_equations(self) -> None:
        """Generate equations for capacitors"""
        capacitors = self.parser.get_capacitors()
        
        for cap_name, cap_component in capacitors.items():
            equation = self._generate_capacitor_equation(cap_component)
            self.component_equations[cap_name] = equation
    
    def _generate_capacitor_equation(self, capacitor: CircuitComponent) -> ComponentEquation:
        """Generate equations for a single capacitor"""
        cap_name = capacitor.name
        capacitance = capacitor.value
        node1 = capacitor.node1
        node2 = capacitor.node2
        
        # Generate voltage names
        v_node1 = self._get_voltage_name(node1)
        v_node2 = self._get_voltage_name(node2)
        v_cap = f"V_{cap_name.lower()}"
        i_cap = f"I_{cap_name.lower()}"
        
        # Voltage equation: V_cap = V_node1 - V_node2
        voltage_equation = f"{v_cap} = {v_node1} - {v_node2}"
        
        # Current equation: I_cap = C * d(V_cap)/dt
        current_equation = f"{i_cap} = {capacitance} * d({v_cap})/dt"
        
        # Discrete current equation: I_cap = C * (V_cap - V_cap_old) / dt
        current_equation_discrete = (
            f"{i_cap} = {capacitance} * "
            f"(({v_node1} - {v_node2}) - {v_cap}_old) / dt"
        )
        
        # Voltage integration equation: V_cap = (1/C) * ∫I_cap dt
        voltage_integration = f"{v_cap} = (1/{capacitance}) * ∫{i_cap} dt"
        
        return ComponentEquation(
            name=cap_name,
            component_type="Capacitor",
            value=capacitance,
            node1=node1,
            node2=node2,
            voltage_equation=voltage_equation,
            current_equation=current_equation,
            current_equation_discrete=current_equation_discrete,
            voltage_integration=voltage_integration
        )
    
    def _generate_kvl_equations(self) -> None:
        """Generate Kirchhoff's Voltage Law equations"""
        # For each loop in the circuit, sum of voltages = 0
        # This is a simplified implementation
        self.kvl_equations = []
        
        # Example: For a simple series circuit
        # V_source - V_R - V_L - V_C = 0
        
    def _generate_kcl_equations(self) -> None:
        """Generate Kirchhoff's Current Law equations"""
        # For each node, sum of currents = 0
        self.kcl_equations = []

        for node_id, node in self.parser.nodes.items():
            # Skip ground nodes only if they have less than 2 connections
            # Ground nodes with multiple connections still need KCL equations
            connected_components = self.parser.get_connected_components(node_id)
            if node.is_ground and len(connected_components) <= 1:
                continue

            if len(connected_components) > 1:
                # Generate detailed KCL equation with component information
                kcl_info = self._generate_detailed_kcl_for_node(node_id, connected_components)
                self.kcl_equations.extend(kcl_info)

    def _generate_detailed_kcl_for_node(self, node_id: int, connected_components) -> List[str]:
        """Generate detailed KCL equation for a specific node with complete component analysis"""
        node_info = []

        # Node header
        node_label = f"Node {node_id}"
        if self.parser.nodes[node_id].is_ground:
            node_label += " (GROUND)"

        # Get node voltage name
        node_voltage = self._get_voltage_name(node_id)

        # List connected components with detailed equations
        component_details = []
        current_terms = []

        node_info.append(f"{node_label} ({node_voltage}):")
        node_info.append("  Connected components and their equations:")

        for comp in connected_components:
            current_name = f"I_{comp.name.lower()}"
            voltage_name = f"V_{comp.name.lower()}"
            comp_type = comp.component_type.value if hasattr(comp.component_type, 'value') else str(comp.component_type)

            # Get other node voltage name
            other_node = comp.node2 if comp.node1 == node_id else comp.node1
            other_voltage = self._get_voltage_name(other_node)

            # Determine current direction and description
            if comp.node1 == node_id:
                direction = "outgoing"
                current_terms.append(f"-{current_name}")
                direction_symbol = "→"
                connection_desc = f"{node_id}{direction_symbol}{comp.node2}"
                voltage_equation = f"{voltage_name} = {node_voltage} - {other_voltage}"
            else:
                direction = "incoming"
                current_terms.append(f"+{current_name}")
                direction_symbol = "→"
                connection_desc = f"{comp.node1}{direction_symbol}{node_id}"
                voltage_equation = f"{voltage_name} = {other_voltage} - {node_voltage}"

            # Generate component-specific equations
            component_equations = self._generate_component_equations(comp, voltage_name, current_name)

            component_details.append(f"    {comp.name} ({comp_type}): {connection_desc}")
            component_details.append(f"      Voltage: {voltage_equation}")
            component_details.extend([f"      {eq}" for eq in component_equations])
            component_details.append(f"      Current direction: {direction} ({current_name})")
            component_details.append("")

        # Generate the KCL equation
        kcl_equation = " ".join(current_terms) + " = 0"

        # Add to node info
        node_info.extend(component_details)
        node_info.append(f"  Kirchhoff's Current Law (KCL):")
        node_info.append(f"    {kcl_equation}")
        node_info.append(f"    Physical meaning: ∑I_incoming = ∑I_outgoing")
        node_info.append("")

        return node_info

    def _generate_component_equations(self, component, voltage_name: str, current_name: str) -> List[str]:
        """Generate detailed equations for a specific component"""
        equations = []
        comp_type = component.component_type.value if hasattr(component.component_type, 'value') else str(component.component_type)

        if comp_type == 'R':
            # Resistor equations
            equations.append(f"Current (Ohm's law): {current_name} = {voltage_name} / {component.value}")
            equations.append(f"Power: P_{component.name.lower()} = {voltage_name} * {current_name} = {voltage_name}² / {component.value}")

        elif comp_type == 'L':
            # Inductor equations
            equations.append(f"Voltage-current relation: {voltage_name} = {component.value} * d({current_name})/dt")
            equations.append(f"Current (continuous): d({current_name})/dt = {voltage_name} / {component.value}")
            equations.append(f"Current (discrete): {current_name} = {current_name}_old + ({voltage_name} * dt) / {component.value}")
            equations.append(f"Energy: E_{component.name.lower()} = (1/2) * {component.value} * {current_name}²")

        elif comp_type == 'C':
            # Capacitor equations
            equations.append(f"Current-voltage relation: {current_name} = {component.value} * d({voltage_name})/dt")
            equations.append(f"Voltage (continuous): d({voltage_name})/dt = {current_name} / {component.value}")
            equations.append(f"Voltage (discrete): {voltage_name} = {voltage_name}_old + ({current_name} * dt) / {component.value}")
            equations.append(f"Energy: E_{component.name.lower()} = (1/2) * {component.value} * {voltage_name}²")

        return equations
    
    def _generate_matrix_equations(self) -> None:
        """Generate matrix form equations for system analysis"""
        # This would generate state-space or impedance matrix equations
        # Placeholder for more advanced matrix formulation
        self.matrix_equations = []
    
    def _get_voltage_name(self, node_id: int) -> str:
        """Get voltage name for a node"""
        if node_id in self.parser.nodes:
            return self.parser.nodes[node_id].voltage_name
        else:
            return f"Vn{node_id}"
    
    def generate_text_equations(self) -> str:
        """Generate plain text formatted equations"""
        text_output = []
        text_output.append("=== ADVANCED CIRCUIT EQUATIONS ===")
        if self.input_filename:
            text_output.append(f"Source file: {self.input_filename}")
        text_output.append("")

        # Component equations
        for comp_name, eq in self.component_equations.items():
            text_output.append(f"{eq.component_type} {comp_name} ({eq.component_type[0]} = {eq.value}):")
            text_output.append(f"  Nodes: {eq.node1} - {eq.node2}")
            text_output.append(f"  Voltage: {eq.voltage_equation}")
            text_output.append(f"  Current (continuous): {eq.current_equation}")
            text_output.append(f"  Current (discrete): {eq.current_equation_discrete}")
            if eq.voltage_integration:
                text_output.append(f"  Integration: {eq.voltage_integration}")
            text_output.append("")

        # KCL equations
        if self.kcl_equations:
            text_output.append("=== KIRCHHOFF'S CURRENT LAW (KCL) ===")
            text_output.append("Complete nodal analysis with component equations:")
            text_output.append("Each node shows:")
            text_output.append("  - All connected components and their types")
            text_output.append("  - Voltage equations for each component")
            text_output.append("  - Current-voltage relationships (Ohm's law, L/C dynamics)")
            text_output.append("  - Energy equations for reactive components")
            text_output.append("  - Current direction and KCL equation")
            text_output.append("")
            for kcl_eq in self.kcl_equations:
                text_output.append(kcl_eq)
            text_output.append("")

        # KVL equations
        if self.kvl_equations:
            text_output.append("=== KIRCHHOFF'S VOLTAGE LAW (KVL) ===")
            for kvl_eq in self.kvl_equations:
                text_output.append(f"  {kvl_eq}")
            text_output.append("")

        return "\n".join(text_output)

    def generate_python_code(self) -> str:
        """Generate Python code for numerical computation - DISABLED"""
        return "# Python code generation is disabled. Only equation formulation is provided."



    def generate_acf2_special_equations(self) -> List[str]:
        """Generate generalized special circuit analysis for any circuit"""
        special_equations = []

        # Always generate special analysis for any circuit
        special_equations.append("=== ADAPTIVE CIRCUIT ANALYSIS ===")
        if self.input_filename:
            special_equations.append(f"Source file: {self.input_filename}")
        special_equations.append("")

        # Generate adaptive analysis based on actual circuit topology
        special_equations.extend(self._generate_adaptive_circuit_analysis())

        return special_equations

    def _generate_adaptive_circuit_analysis(self) -> List[str]:
        """Generate adaptive circuit analysis for any circuit topology"""
        equations = []

        # Analyze circuit topology
        inductors = self.parser.get_inductors()
        capacitors = self.parser.get_capacitors()
        resistors = self.parser.get_resistors()

        # Generate circuit topology analysis
        equations.extend(self._generate_circuit_topology_analysis(inductors, capacitors, resistors))

        # Generate comprehensive KVL analysis
        equations.extend(self._generate_comprehensive_kvl_analysis())

        # Generate loop analysis
        equations.extend(self._generate_loop_analysis())

        # Generate energy analysis
        equations.extend(self._generate_energy_analysis())

        # Generate adaptive system matrix analysis
        equations.extend(self._generate_adaptive_system_matrix_analysis(inductors, capacitors, resistors))

        # Generate specialized analysis based on detected patterns
        equations.extend(self._generate_pattern_based_analysis(inductors, capacitors, resistors))

        return equations

    def _generate_circuit_topology_analysis(self, inductors: dict, capacitors: dict, resistors: dict) -> List[str]:
        """Generate detailed circuit topology analysis"""
        equations = []

        equations.append("Circuit Topology Analysis:")
        equations.append("=" * 50)
        equations.append("")

        # Component count analysis
        equations.append("Component Count:")
        equations.append(f"  Inductors: {len(inductors)}")
        equations.append(f"  Capacitors: {len(capacitors)}")
        equations.append(f"  Resistors: {len(resistors)}")
        equations.append("")

        # Analyze specific component patterns
        neutral_inductors = self._find_neutral_inductors(inductors)
        phase_inductors = self._find_phase_inductors(inductors)

        equations.append("Inductor Classification:")
        if neutral_inductors:
            equations.append(f"  Neutral inductors found: {', '.join(neutral_inductors)}")
        else:
            equations.append("  No neutral inductors detected")

        if phase_inductors:
            equations.append(f"  Phase inductors found: {', '.join(phase_inductors)}")
        else:
            equations.append("  No phase inductors detected")
        equations.append("")

        # Analyze circuit structure
        circuit_structure = self._analyze_circuit_structure(inductors, capacitors, resistors)
        equations.extend(circuit_structure)

        return equations

    def _find_neutral_inductors(self, inductors: dict) -> List[str]:
        """Find neutral inductors (Ln1, Ln2, etc.)"""
        neutral_inductors = []
        for name in inductors.keys():
            if name.lower().startswith('ln'):
                neutral_inductors.append(name)
        return neutral_inductors

    def _find_phase_inductors(self, inductors: dict) -> List[str]:
        """Find phase inductors (Lu1, Lv1, Lw1, Lu2, etc.)"""
        phase_inductors = []
        phase_patterns = ['lu', 'lv', 'lw']
        for name in inductors.keys():
            if any(name.lower().startswith(pattern) for pattern in phase_patterns):
                phase_inductors.append(name)
        return phase_inductors

    def _analyze_circuit_structure(self, inductors: dict, capacitors: dict, resistors: dict) -> List[str]:
        """Analyze overall circuit structure"""
        equations = []

        equations.append("Circuit Structure Analysis:")

        # Determine circuit type
        neutral_inductors = self._find_neutral_inductors(inductors)
        phase_inductors = self._find_phase_inductors(inductors)

        if len(neutral_inductors) == 0:
            equations.append("  Structure: No neutral inductors - Independent phase system")
        elif len(neutral_inductors) == 1:
            equations.append("  Structure: Single neutral inductor - Star-connected system")
        elif len(neutral_inductors) == 2:
            equations.append("  Structure: Dual neutral inductors - Primary/Secondary system")
        else:
            equations.append(f"  Structure: Multiple neutral inductors ({len(neutral_inductors)}) - Complex system")

        # Analyze phase structure
        primary_phases = [name for name in phase_inductors if '1' in name]
        secondary_phases = [name for name in phase_inductors if '2' in name]

        if primary_phases and secondary_phases:
            equations.append(f"  Primary phases: {len(primary_phases)} ({', '.join(primary_phases)})")
            equations.append(f"  Secondary phases: {len(secondary_phases)} ({', '.join(secondary_phases)})")
        elif primary_phases:
            equations.append(f"  Single system phases: {len(primary_phases)} ({', '.join(primary_phases)})")
        else:
            equations.append("  No standard phase naming detected")

        equations.append("")
        return equations

    def _generate_comprehensive_kvl_analysis(self) -> List[str]:
        """Generate comprehensive Kirchhoff's Voltage Law analysis"""
        equations = []

        equations.append("Extended Kirchhoff's Voltage Law (KVL) Analysis:")
        equations.append("For each independent loop in the circuit:")
        equations.append("")

        # Find all unique loops by analyzing circuit topology
        loops = self._identify_circuit_loops()

        for i, loop in enumerate(loops, 1):
            equations.append(f"Loop {i}: {' → '.join(loop)}")
            kvl_equation = self._generate_kvl_for_loop(loop)
            equations.append(f"  KVL: {kvl_equation}")
            equations.append("")

        return equations

    def _generate_loop_analysis(self) -> List[str]:
        """Generate loop current analysis"""
        equations = []

        equations.append("Loop Current Analysis:")
        equations.append("Independent current relationships:")
        equations.append("")

        # Analyze current relationships based on circuit topology
        current_relationships = self._analyze_current_relationships()
        for relationship in current_relationships:
            equations.append(f"  {relationship}")

        equations.append("")
        return equations

    def _generate_energy_analysis(self) -> List[str]:
        """Generate energy analysis for reactive components"""
        equations = []

        inductors = self.parser.get_inductors()
        capacitors = self.parser.get_capacitors()

        if inductors or capacitors:
            equations.append("Energy Analysis:")
            equations.append("Total system energy:")
            equations.append("")

            # Magnetic energy from inductors
            if inductors:
                equations.append("Magnetic energy (inductors):")
                total_magnetic = []
                for name, inductor in inductors.items():
                    energy_term = f"(1/2) * {inductor.value} * I_{name.lower()}²"
                    equations.append(f"  E_{name.lower()} = {energy_term}")
                    total_magnetic.append(f"E_{name.lower()}")

                equations.append(f"  Total magnetic energy: E_L = {' + '.join(total_magnetic)}")
                equations.append("")

            # Electric energy from capacitors
            if capacitors:
                equations.append("Electric energy (capacitors):")
                total_electric = []
                for name, capacitor in capacitors.items():
                    energy_term = f"(1/2) * {capacitor.value} * V_{name.lower()}²"
                    equations.append(f"  E_{name.lower()} = {energy_term}")
                    total_electric.append(f"E_{name.lower()}")

                equations.append(f"  Total electric energy: E_C = {' + '.join(total_electric)}")
                equations.append("")

            # Total system energy
            energy_components = []
            if inductors:
                energy_components.append("E_L")
            if capacitors:
                energy_components.append("E_C")

            equations.append(f"Total system energy: E_total = {' + '.join(energy_components)}")
            equations.append("")

        return equations

    def _generate_system_matrix_analysis(self) -> List[str]:
        """Generate system matrix analysis for multi-component circuits"""
        equations = []

        inductors = self.parser.get_inductors()
        capacitors = self.parser.get_capacitors()

        equations.append("System Matrix Analysis:")
        equations.append("")

        # State vector analysis
        state_variables = []
        if inductors:
            for name in inductors.keys():
                state_variables.append(f"I_{name.lower()}")
        if capacitors:
            for name in capacitors.keys():
                state_variables.append(f"V_{name.lower()}")

        if state_variables:
            equations.append("State vector:")
            equations.append(f"x = [{', '.join(state_variables)}]ᵀ")
            equations.append("")

            equations.append("State-space representation:")
            equations.append("dx/dt = A*x + B*u")
            equations.append("y = C*x + D*u")
            equations.append("")
            equations.append("Where:")
            equations.append("  x = state vector (inductor currents, capacitor voltages)")
            equations.append("  u = input vector (voltage sources, current sources)")
            equations.append("  y = output vector (measured variables)")
            equations.append("  A = system matrix (circuit topology dependent)")
            equations.append("  B = input matrix")
            equations.append("  C = output matrix")
            equations.append("  D = feedthrough matrix")
            equations.append("")

        return equations

    def _generate_adaptive_system_matrix_analysis(self, inductors: dict, capacitors: dict, resistors: dict) -> List[str]:
        """Generate adaptive system matrix analysis based on actual circuit components"""
        equations = []

        equations.append("Adaptive System Matrix Analysis:")
        equations.append("=" * 50)
        equations.append("")

        # Analyze neutral inductors
        neutral_inductors = self._find_neutral_inductors(inductors)
        phase_inductors = self._find_phase_inductors(inductors)

        if len(inductors) > 1:
            equations.extend(self._generate_inductor_matrix_analysis(inductors, neutral_inductors, phase_inductors))

        if len(capacitors) > 1:
            equations.extend(self._generate_capacitor_matrix_analysis(capacitors))

        # Generate combined system analysis
        if len(inductors) > 1 or len(capacitors) > 1:
            equations.extend(self._generate_combined_system_analysis(inductors, capacitors, resistors))

        return equations

    def _generate_inductor_matrix_analysis(self, inductors: dict, neutral_inductors: List[str], phase_inductors: List[str]) -> List[str]:
        """Generate inductor matrix analysis with adaptive structure"""
        equations = []

        equations.append("Inductor Matrix Analysis:")
        equations.append("")

        if not neutral_inductors:
            # No neutral inductors - independent system
            equations.append("Independent Inductor System (No Neutral Coupling):")
            equations.append("Each inductor operates independently:")
            for name, inductor in inductors.items():
                equations.append(f"  d(I{name.lower()})/dt = V{name.lower()} / {inductor.value}")
            equations.append("")

        elif len(neutral_inductors) == 1:
            # Single neutral inductor system
            equations.extend(self._generate_single_neutral_matrix(inductors, neutral_inductors[0], phase_inductors))

        elif len(neutral_inductors) == 2:
            # Dual neutral inductor system (primary/secondary)
            equations.extend(self._generate_dual_neutral_matrix(inductors, neutral_inductors, phase_inductors))

        else:
            # Multiple neutral inductors - complex system
            equations.extend(self._generate_complex_neutral_matrix(inductors, neutral_inductors, phase_inductors))

        return equations

    def _generate_capacitor_matrix_analysis(self, capacitors: dict) -> List[str]:
        """Generate capacitor matrix analysis"""
        equations = []

        equations.append("Capacitor Matrix Analysis:")
        equations.append("")

        if len(capacitors) > 1:
            equations.append("Multiple capacitors detected:")
            for name, capacitor in capacitors.items():
                equations.append(f"  {name}: d(V{name.lower()})/dt = I{name.lower()} / {capacitor.value}")

            equations.append("")
            equations.append("Capacitor voltage state vector:")
            cap_voltages = [f"V{name.lower()}" for name in capacitors.keys()]
            equations.append(f"V_cap = [{', '.join(cap_voltages)}]ᵀ")

        else:
            equations.append("Single capacitor system - no matrix analysis needed")

        equations.append("")
        return equations

    def _generate_single_neutral_matrix(self, inductors: dict, neutral_name: str, phase_inductors: List[str]) -> List[str]:
        """Generate matrix for single neutral inductor system"""
        equations = []

        equations.append(f"Single Neutral System (with {neutral_name}):")
        equations.append("")

        # Filter phase inductors that are connected to this neutral
        connected_phases = [name for name in phase_inductors if name in inductors]

        if len(connected_phases) >= 2:
            equations.append("Matrix form with neutral coupling:")

            # Generate matrix structure
            matrix_size = len(connected_phases)
            equations.append(f"System size: {matrix_size}×{matrix_size}")
            equations.append("")

            # Current relationship
            current_sum = " + ".join([f"I{name.lower()}" for name in connected_phases])
            equations.append(f"Current constraint: {current_sum} = I{neutral_name.lower()}")
            equations.append("")

            # Matrix equation structure
            equations.append("Matrix equation structure:")
            self._generate_matrix_display(equations, connected_phases, neutral_name, inductors)

        else:
            equations.append("Insufficient phase inductors for matrix analysis")

        equations.append("")
        return equations

    def _generate_matrix_display(self, equations: List[str], phase_names: List[str], neutral_name: str, inductors: dict):
        """Generate visual matrix display"""
        n = len(phase_names)

        # State vector
        state_vars = [f"d(I{name.lower()})/dt" for name in phase_names]
        equations.append(f"⎡{state_vars[0]}⎤")
        for i in range(1, n):
            if i == n-1:
                equations.append(f"⎣{state_vars[i]}⎦")
            else:
                equations.append(f"⎢{state_vars[i]}⎥")
        equations.append("")

        # Matrix structure description
        equations.append("Matrix structure:")
        for i, name_i in enumerate(phase_names):
            row_desc = f"Row {i+1} ({name_i}): "
            for j, name_j in enumerate(phase_names):
                if i == j:
                    # Diagonal element
                    if neutral_name in inductors:
                        row_desc += f"({inductors[name_i].value}+{inductors[neutral_name].value}) "
                    else:
                        row_desc += f"{inductors[name_i].value} "
                else:
                    # Off-diagonal element
                    if neutral_name in inductors:
                        row_desc += f"{inductors[neutral_name].value} "
                    else:
                        row_desc += "0 "
            equations.append(row_desc)
        equations.append("")

    def _generate_pattern_based_analysis(self, inductors: dict, capacitors: dict, resistors: dict) -> List[str]:
        """Generate unified adaptive analysis for any circuit topology"""
        equations = []

        equations.append("Unified Circuit Analysis:")
        equations.append("=" * 50)
        equations.append("")

        # Generate unified adaptive analysis for any circuit
        equations.extend(self._generate_unified_adaptive_analysis(inductors, capacitors, resistors))

        return equations

    def _generate_unified_adaptive_analysis(self, inductors: dict, capacitors: dict, resistors: dict) -> List[str]:
        """Generate unified adaptive analysis for any circuit topology"""
        equations = []

        # Analyze circuit components
        neutral_inductors = self._find_neutral_inductors(inductors)
        phase_inductors = self._find_phase_inductors(inductors)

        # Analyze system structure
        primary_phases = [name for name in phase_inductors if '1' in name]
        secondary_phases = [name for name in phase_inductors if '2' in name]

        equations.append("Circuit Component Analysis:")
        equations.append(f"  Total inductors: {len(inductors)}")
        equations.append(f"  Neutral inductors: {neutral_inductors if neutral_inductors else 'None'}")
        equations.append(f"  Phase inductors: {phase_inductors if phase_inductors else 'None'}")
        equations.append(f"  Primary phases: {primary_phases if primary_phases else 'None'}")
        equations.append(f"  Secondary phases: {secondary_phases if secondary_phases else 'None'}")
        equations.append(f"  Capacitors: {len(capacitors)}")
        equations.append(f"  Resistors: {len(resistors)}")
        equations.append("")

        # Generate appropriate analysis based on circuit structure
        if len(inductors) > 1:
            equations.extend(self._generate_multi_inductor_analysis(inductors, neutral_inductors, phase_inductors, primary_phases, secondary_phases))
        elif len(inductors) == 1:
            equations.extend(self._generate_single_inductor_analysis(inductors))

        # Add capacitor analysis if present
        if capacitors:
            equations.extend(self._generate_capacitor_analysis(capacitors))

        # Add resistor analysis if present
        if resistors:
            equations.extend(self._generate_resistor_analysis(resistors))

        # Generate system coupling analysis
        equations.extend(self._generate_system_coupling_analysis(inductors, capacitors, resistors))

        return equations

    def _generate_multi_inductor_analysis(self, inductors: dict, neutral_inductors: List[str],
                                        phase_inductors: List[str], primary_phases: List[str],
                                        secondary_phases: List[str]) -> List[str]:
        """Generate analysis for circuits with multiple inductors"""
        equations = []

        equations.append("Multi-Inductor System Analysis:")
        equations.append("")

        if not neutral_inductors:
            # No neutral inductors - independent system
            equations.append("Independent Inductor System (No Neutral Coupling):")
            equations.append("Each inductor operates independently:")
            for name, inductor in inductors.items():
                equations.append(f"  d(I{name.lower()})/dt = V{name.lower()} / {inductor.value}")
            equations.append("")

        elif len(neutral_inductors) == 1:
            # Single neutral inductor system
            equations.extend(self._generate_single_neutral_system_analysis(inductors, neutral_inductors[0], phase_inductors))

        elif len(neutral_inductors) == 2:
            # Dual neutral inductor system (primary/secondary)
            equations.extend(self._generate_dual_neutral_system_analysis(inductors, neutral_inductors, primary_phases, secondary_phases))

        else:
            # Multiple neutral inductors - complex system
            equations.extend(self._generate_complex_neutral_system_analysis(inductors, neutral_inductors))

        return equations

    def _generate_single_inductor_analysis(self, inductors: dict) -> List[str]:
        """Generate analysis for single inductor circuits"""
        equations = []

        equations.append("Single Inductor System:")
        inductor_name = list(inductors.keys())[0]
        inductor = inductors[inductor_name]

        equations.append(f"  {inductor_name}: d(I{inductor_name.lower()})/dt = V{inductor_name.lower()} / {inductor.value}")
        equations.append("")

        return equations

    def _generate_capacitor_analysis(self, capacitors: dict) -> List[str]:
        """Generate capacitor analysis"""
        equations = []

        equations.append("Capacitor Analysis:")
        if len(capacitors) == 1:
            cap_name = list(capacitors.keys())[0]
            cap = capacitors[cap_name]
            equations.append(f"  Single capacitor: {cap_name}")
            equations.append(f"  d(V{cap_name.lower()})/dt = I{cap_name.lower()} / {cap.value}")
        else:
            equations.append(f"  Multiple capacitors ({len(capacitors)}):")
            for name, cap in capacitors.items():
                equations.append(f"    {name}: d(V{name.lower()})/dt = I{name.lower()} / {cap.value}")

        equations.append("")
        return equations

    def _generate_resistor_analysis(self, resistors: dict) -> List[str]:
        """Generate resistor analysis"""
        equations = []

        equations.append("Resistor Analysis:")
        if len(resistors) == 1:
            res_name = list(resistors.keys())[0]
            res = resistors[res_name]
            equations.append(f"  Single resistor: {res_name}")
            equations.append(f"  I{res_name.lower()} = V{res_name.lower()} / {res.value}")
        else:
            equations.append(f"  Multiple resistors ({len(resistors)}):")
            for name, res in resistors.items():
                equations.append(f"    {name}: I{name.lower()} = V{name.lower()} / {res.value}")

        equations.append("")
        return equations

    def _generate_system_coupling_analysis(self, inductors: dict, capacitors: dict, resistors: dict) -> List[str]:
        """Generate system coupling analysis"""
        equations = []

        equations.append("System Coupling Analysis:")

        total_reactive = len(inductors) + len(capacitors)
        if total_reactive > 1:
            equations.append("Reactive component coupling:")
            equations.append(f"  Total reactive components: {total_reactive}")
            equations.append(f"  Inductors: {len(inductors)} (magnetic energy storage)")
            equations.append(f"  Capacitors: {len(capacitors)} (electric energy storage)")
            equations.append("")

            # State-space representation
            equations.append("State-space representation:")
            equations.append("dx/dt = A*x + B*u")
            equations.append("Where x contains:")
            for name in inductors.keys():
                equations.append(f"  I{name.lower()} (inductor current)")
            for name in capacitors.keys():
                equations.append(f"  V{name.lower()} (capacitor voltage)")
        else:
            equations.append("Single reactive component - no coupling analysis needed")

        equations.append("")
        return equations

    def _generate_single_neutral_system_analysis(self, inductors: dict, neutral_name: str, phase_inductors: List[str]) -> List[str]:
        """Generate analysis for single neutral inductor system"""
        equations = []

        equations.append(f"Single Neutral System (with {neutral_name}):")
        equations.append("")

        # Filter phase inductors that are connected to this neutral
        connected_phases = [name for name in phase_inductors if name in inductors]

        if len(connected_phases) >= 2:
            equations.append("Matrix form with neutral coupling:")

            # Current constraint
            current_sum = " + ".join([f"I{name.lower()}" for name in connected_phases])
            equations.append(f"Current constraint: {current_sum} = I{neutral_name.lower()}")
            equations.append("")

            # Matrix equation
            equations.append("Matrix equation:")
            self._generate_adaptive_matrix_display(equations, connected_phases, neutral_name, inductors)

        else:
            equations.append("Insufficient phase inductors for matrix analysis")

        equations.append("")
        return equations

    def _generate_dual_neutral_system_analysis(self, inductors: dict, neutral_inductors: List[str],
                                             primary_phases: List[str], secondary_phases: List[str]) -> List[str]:
        """Generate analysis for dual neutral inductor system"""
        equations = []

        equations.append("Dual Neutral System Analysis:")
        equations.append("")

        # Identify primary and secondary neutrals
        primary_neutral = None
        secondary_neutral = None

        for neutral in neutral_inductors:
            if '1' in neutral:
                primary_neutral = neutral
            elif '2' in neutral:
                secondary_neutral = neutral

        equations.append("System Structure:")
        equations.append(f"  Primary phases: {', '.join(primary_phases) if primary_phases else 'None'}")
        equations.append(f"  Secondary phases: {', '.join(secondary_phases) if secondary_phases else 'None'}")
        equations.append(f"  Primary neutral: {primary_neutral if primary_neutral else 'None'}")
        equations.append(f"  Secondary neutral: {secondary_neutral if secondary_neutral else 'None'}")
        equations.append("")

        # Generate primary system analysis
        if primary_phases and primary_neutral:
            equations.append("Primary System (with neutral coupling):")
            self._generate_adaptive_matrix_display(equations, primary_phases, primary_neutral, inductors)
            equations.append("")
        elif primary_phases:
            equations.append("Primary System (independent phases):")
            for name in primary_phases:
                equations.append(f"  d(I{name.lower()})/dt = V{name.lower()} / {inductors[name].value}")
            equations.append("")

        # Generate secondary system analysis
        if secondary_phases and secondary_neutral:
            equations.append("Secondary System (with neutral coupling):")
            self._generate_adaptive_matrix_display(equations, secondary_phases, secondary_neutral, inductors)
            equations.append("")
        elif secondary_phases:
            equations.append("Secondary System (independent phases):")
            for name in secondary_phases:
                equations.append(f"  d(I{name.lower()})/dt = V{name.lower()} / {inductors[name].value}")
            equations.append("")

        return equations

    def _generate_complex_neutral_system_analysis(self, inductors: dict, neutral_inductors: List[str]) -> List[str]:
        """Generate analysis for complex neutral inductor system"""
        equations = []

        equations.append(f"Complex Neutral System ({len(neutral_inductors)} neutrals):")
        equations.append("")

        equations.append("Multiple neutral inductors detected:")
        for neutral in neutral_inductors:
            equations.append(f"  {neutral}")

        equations.append("")
        equations.append("Complex coupling matrix required.")
        equations.append("System size: {}×{}".format(len(inductors), len(inductors)))
        equations.append("")

        return equations

    def _generate_adaptive_matrix_display(self, equations: List[str], phase_names: List[str], neutral_name: str, inductors: dict):
        """Generate adaptive matrix display for any circuit configuration"""
        n = len(phase_names)

        if n == 0:
            equations.append("No phase inductors to display")
            return

        # State vector
        state_vars = [f"d(I{name.lower()})/dt" for name in phase_names]
        if n == 1:
            equations.append(f"[{state_vars[0]}]")
        else:
            equations.append(f"⎡{state_vars[0]}⎤")
            for i in range(1, n):
                if i == n-1:
                    equations.append(f"⎣{state_vars[i]}⎦")
                else:
                    equations.append(f"⎢{state_vars[i]}⎥")

        equations.append("")
        equations.append("Matrix structure:")

        # Generate matrix elements
        for i, name_i in enumerate(phase_names):
            row_desc = f"Row {i+1} ({name_i}): "
            for j in range(len(phase_names)):
                if i == j:
                    # Diagonal element
                    if neutral_name in inductors:
                        row_desc += f"({inductors[name_i].value}+{inductors[neutral_name].value}) "
                    else:
                        row_desc += f"{inductors[name_i].value} "
                else:
                    # Off-diagonal element
                    if neutral_name in inductors:
                        row_desc += f"{inductors[neutral_name].value} "
                    else:
                        row_desc += "0 "
            equations.append(row_desc)

        equations.append("")

        # Voltage vector
        voltage_vars = [f"V{name.lower()}_effective" for name in phase_names]
        if n == 1:
            equations.append(f"Voltage vector: [{voltage_vars[0]}]")
        else:
            equations.append("Voltage vector:")
            equations.append(f"⎡{voltage_vars[0]}⎤")
            for i in range(1, n):
                if i == n-1:
                    equations.append(f"⎣{voltage_vars[i]}⎦")
                else:
                    equations.append(f"⎢{voltage_vars[i]}⎥")

        equations.append("")

    def _generate_single_system_acf_analysis(self, phase_inductors: List[str], neutral_inductors: List[str], inductors: dict) -> List[str]:
        """Generate analysis for single system - DEPRECATED, use unified analysis instead"""
        equations = []
        equations.append("Note: This function is deprecated. Using unified adaptive analysis.")
        equations.append("")

        # Redirect to unified analysis
        if neutral_inductors:
            equations.extend(self._generate_single_neutral_system_analysis(inductors, neutral_inductors[0], phase_inductors))
        else:
            equations.append("Independent phase system:")
            for name in phase_inductors:
                equations.append(f"  d(I{name.lower()})/dt = V{name.lower()} / {inductors[name].value}")
            equations.append("")

        return equations

    def _generate_dual_system_acf_analysis(self, primary_phases: List[str], secondary_phases: List[str],
                                         neutral_inductors: List[str], inductors: dict) -> List[str]:
        """Generate analysis for dual system - DEPRECATED, use unified analysis instead"""
        equations = []
        equations.append("Note: This function is deprecated. Using unified adaptive analysis.")
        equations.append("")

        # Redirect to unified analysis
        equations.extend(self._generate_dual_neutral_system_analysis(inductors, neutral_inductors, primary_phases, secondary_phases))

        return equations

    def _generate_dual_neutral_matrix(self, inductors: dict, neutral_inductors: List[str], phase_inductors: List[str]) -> List[str]:
        """Generate matrix for dual neutral inductor system"""
        equations = []

        equations.append("Dual Neutral System:")
        equations.append("")

        primary_neutral = None
        secondary_neutral = None

        for neutral in neutral_inductors:
            if '1' in neutral:
                primary_neutral = neutral
            elif '2' in neutral:
                secondary_neutral = neutral

        primary_phases = [name for name in phase_inductors if '1' in name]
        secondary_phases = [name for name in phase_inductors if '2' in name]

        equations.append(f"Primary neutral: {primary_neutral}")
        equations.append(f"Secondary neutral: {secondary_neutral}")
        equations.append(f"Primary phases: {', '.join(primary_phases)}")
        equations.append(f"Secondary phases: {', '.join(secondary_phases)}")
        equations.append("")

        # Generate separate matrices for primary and secondary
        if primary_phases and primary_neutral:
            equations.append("Primary system matrix:")
            self._generate_matrix_display(equations, primary_phases, primary_neutral, inductors)

        if secondary_phases and secondary_neutral:
            equations.append("Secondary system matrix:")
            self._generate_matrix_display(equations, secondary_phases, secondary_neutral, inductors)

        return equations

    def _generate_complex_neutral_matrix(self, inductors: dict, neutral_inductors: List[str], phase_inductors: List[str]) -> List[str]:
        """Generate matrix for complex neutral inductor system"""
        equations = []

        equations.append(f"Complex Neutral System ({len(neutral_inductors)} neutrals):")
        equations.append("")

        equations.append("Multiple neutral inductors detected:")
        for neutral in neutral_inductors:
            equations.append(f"  {neutral}")

        equations.append("")
        equations.append("Complex coupling matrix required.")
        equations.append("System size: {}×{}".format(len(inductors), len(inductors)))
        equations.append("")

        return equations

    def _generate_combined_system_analysis(self, inductors: dict, capacitors: dict, resistors: dict) -> List[str]:
        """Generate combined system analysis"""
        equations = []

        equations.append("Combined System Analysis:")
        equations.append("")

        total_states = len(inductors) + len(capacitors)
        equations.append(f"Total system states: {total_states}")
        equations.append(f"  Inductor currents: {len(inductors)}")
        equations.append(f"  Capacitor voltages: {len(capacitors)}")
        equations.append("")

        if total_states > 1:
            equations.append("State-space representation:")
            equations.append("dx/dt = A*x + B*u")
            equations.append("")
            equations.append("Where x contains:")
            for name in inductors.keys():
                equations.append(f"  I{name.lower()} (inductor current)")
            for name in capacitors.keys():
                equations.append(f"  V{name.lower()} (capacitor voltage)")

        equations.append("")
        return equations

    def _generate_acf_specialized_analysis(self, inductors: dict) -> List[str]:
        """Generate specialized analysis for ACF-type circuits - DEPRECATED"""
        equations = []

        equations.append("ACF-Type Circuit Specialized Analysis (Legacy):")
        equations.append("Note: This function is deprecated. Use adaptive analysis instead.")
        equations.append("")

        # Redirect to adaptive analysis
        neutral_inductors = self._find_neutral_inductors(inductors)
        phase_inductors = self._find_phase_inductors(inductors)

        # Check for primary/secondary structure
        has_secondary = any(name in inductors for name in ['Lu2', 'Lv2', 'Lw2'])

        if has_secondary:
            equations.append("Detected: Primary/Secondary system")
            primary_phases = [name for name in phase_inductors if '1' in name]
            secondary_phases = [name for name in phase_inductors if '2' in name]
            equations.extend(self._generate_dual_system_acf_analysis(primary_phases, secondary_phases, neutral_inductors, inductors))
        else:
            equations.append("Detected: Single system")
            primary_phases = [name for name in phase_inductors if '1' in name]
            equations.extend(self._generate_single_system_acf_analysis(primary_phases, neutral_inductors, inductors))

        return equations

    def _identify_circuit_loops(self) -> List[List[str]]:
        """Identify independent loops in the circuit"""
        # Simplified loop identification - in practice, this would use graph theory
        loops = []

        # For now, generate basic loops based on component connections
        # This is a simplified implementation
        components = []
        for comp_dict in [self.parser.get_resistors(), self.parser.get_inductors(), self.parser.get_capacitors()]:
            components.extend(comp_dict.keys())

        if len(components) >= 3:
            # Create a simple loop with first few components
            loops.append(components[:3])

        return loops

    def _generate_kvl_for_loop(self, loop: List[str]) -> str:
        """Generate KVL equation for a specific loop"""
        voltage_terms = []
        for component_name in loop:
            voltage_terms.append(f"V_{component_name.lower()}")

        return f"{' + '.join(voltage_terms)} = 0"

    def _analyze_current_relationships(self) -> List[str]:
        """Analyze current relationships in the circuit"""
        relationships = []

        # Analyze each node for current relationships
        for node_id, node in self.parser.nodes.items():
            if node.is_ground:
                continue

            connected_components = self.parser.get_connected_components(node_id)
            if len(connected_components) > 1:
                # Generate current relationship for this node
                currents = []
                for comp in connected_components:
                    current_name = f"I_{comp.name.lower()}"
                    if comp.node1 == node_id:
                        currents.append(f"-{current_name}")
                    else:
                        currents.append(f"+{current_name}")

                if currents:
                    relationship = f"Node {node_id}: {' '.join(currents)} = 0"
                    relationships.append(relationship)

        return relationships

    def _generate_acf2_regular_analysis(self) -> List[str]:
        """Generate analysis for regular ACF2 circuit (4 inductors) - DEPRECATED"""
        equations = []

        equations.append("ACF2 Regular Analysis (Legacy - 4 inductors):")
        equations.append("Note: This function is deprecated. Use adaptive analysis instead.")
        equations.append("")

        # Redirect to adaptive analysis
        inductors = self.parser.get_inductors()
        neutral_inductors = self._find_neutral_inductors(inductors)
        phase_inductors = self._find_phase_inductors(inductors)
        primary_phases = [name for name in phase_inductors if '1' in name]

        equations.append("Detected components:")
        equations.append(f"  Primary phases: {primary_phases}")
        equations.append(f"  Neutral inductors: {neutral_inductors}")
        equations.append("")

        # Use adaptive analysis
        equations.extend(self._generate_single_system_acf_analysis(primary_phases, neutral_inductors, inductors))

        # Add common analysis sections
        equations.extend(self._add_common_analysis())

        return equations

    def _generate_acf2_kawai_analysis(self, inductors: dict) -> List[str]:
        """Generate analysis for ACF2_kawai circuit (8 inductors) - DEPRECATED"""
        equations = []

        equations.append("ACF2_kawai Analysis (Legacy - 8 inductors):")
        equations.append("Note: This function is deprecated. Use adaptive analysis instead.")
        equations.append("")

        # Redirect to adaptive analysis
        neutral_inductors = self._find_neutral_inductors(inductors)
        phase_inductors = self._find_phase_inductors(inductors)
        primary_phases = [name for name in phase_inductors if '1' in name]
        secondary_phases = [name for name in phase_inductors if '2' in name]

        equations.append("Detected components:")
        equations.append(f"  Primary phases: {primary_phases}")
        equations.append(f"  Secondary phases: {secondary_phases}")
        equations.append(f"  Neutral inductors: {neutral_inductors}")
        equations.append("")

        # Use adaptive analysis
        equations.extend(self._generate_dual_system_acf_analysis(primary_phases, secondary_phases, neutral_inductors, inductors))

        return equations

    def _add_common_analysis(self) -> List[str]:
        """Add common analysis sections (capacitor relationships, etc.)"""
        equations = []

        # Capacitor current relationships based on actual circuit topology
        equations.append("Capacitor current relationships (KCL):")
        capacitor_relationships = self._generate_capacitor_current_relationships()
        for relationship in capacitor_relationships:
            equations.append(relationship)
        equations.append("")

        # Capacitor voltage equations
        equations.append("Capacitor voltage equations:")
        equations.append("Vcu2 = (1/Cu2) * ∫ICu2 dt")
        equations.append("Vcv2 = (1/Cv2) * ∫ICv2 dt")
        equations.append("Vcw2 = (1/Cw2) * ∫ICw2 dt")
        equations.append("")

        return equations

    def _generate_capacitor_current_relationships(self) -> List[str]:
        """Generate capacitor current relationships based on actual circuit topology"""
        relationships = []

        # Get all capacitors
        capacitors = self.parser.get_capacitors()

        for cap_name, cap_component in capacitors.items():
            # Find the non-ground node where the capacitor is connected
            non_ground_node = None
            if cap_component.node1 != 11:  # Assuming 11 is ground
                non_ground_node = cap_component.node1
            elif cap_component.node2 != 11:
                non_ground_node = cap_component.node2

            if non_ground_node is None:
                continue  # Skip if both nodes are ground

            # Get all components connected to this non-ground node
            connected_components = self.parser.get_connected_components(non_ground_node)

            # Build KCL equation for this node and extract capacitor current relationship
            incoming_currents = []
            outgoing_currents = []

            for comp in connected_components:
                current_name = f"I{comp.name.lower()}"

                if comp.name == cap_name:
                    # This is the capacitor itself - determine its direction
                    if comp.node1 == non_ground_node:
                        outgoing_currents.append(current_name)
                    else:
                        incoming_currents.append(current_name)
                else:
                    # Other components
                    if comp.node1 == non_ground_node:
                        outgoing_currents.append(current_name)
                    else:
                        incoming_currents.append(current_name)

            # Generate relationship: capacitor current = sum of incoming - sum of other outgoing
            cap_current = f"I{cap_name}"
            other_outgoing = [curr for curr in outgoing_currents if curr != f"I{cap_name.lower()}"]

            if len(incoming_currents) == 1 and len(other_outgoing) == 0:
                # Simple case: I_cap = I_incoming
                relationships.append(f"{cap_current} = {incoming_currents[0]}")
            elif len(incoming_currents) == 1 and len(other_outgoing) == 1:
                # Two-component case: I_cap = I_incoming - I_other_outgoing
                relationships.append(f"{cap_current} = {incoming_currents[0]} - {other_outgoing[0]}")
            else:
                # More complex case
                incoming_str = " + ".join(incoming_currents) if incoming_currents else "0"
                outgoing_str = " + ".join(other_outgoing) if other_outgoing else "0"
                if outgoing_str != "0":
                    relationships.append(f"{cap_current} = {incoming_str} - ({outgoing_str})")
                else:
                    relationships.append(f"{cap_current} = {incoming_str}")

        return relationships

if __name__ == "__main__":
    # Test the advanced equation generator
    from advanced_circuit_parser import AdvancedCircuitParser

    parser = AdvancedCircuitParser()
    parser.parse_file("input/ACF2.cct")

    generator = AdvancedEquationGenerator(parser, "input/ACF2.cct")
    equations = generator.generate_all_equations()

    print(generator.generate_text_equations())

    # Generate adaptive special equations
    special_eqs = generator.generate_acf2_special_equations()
    if special_eqs:
        print("\n".join(special_eqs))
